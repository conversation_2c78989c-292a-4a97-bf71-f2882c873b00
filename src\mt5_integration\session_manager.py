"""
MT5 Session Manager
Manages MT5 sessions and account switching to prevent connection issues
"""

import asyncio
import MetaTrader5 as mt5
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager
from datetime import datetime, timedelta

from logging_system.logger import get_logger
from account_management.models import TradingAccount
from diagnostics.account_switch_diagnostics import account_switch_diagnostics

logger = get_logger(__name__)


class MT5SessionManager:
    """Manages MT5 sessions and account switching"""
    
    def __init__(self):
        self.current_session_account = None
        self.session_lock = asyncio.Lock()
        self.session_start_time = None
        self.max_session_duration = 30 * 60  # 30 minutes max session
        self.connection_retries = 3
        self.retry_delay = 5  # seconds
        
    async def ensure_account_session(self, account: TradingAccount, mt5_client) -> bool:
        """Ensure we have a valid session for the specified account"""
        async with self.session_lock:
            logger.info(f"🔄 SESSION: Ensuring session for account {account.account_id} ({account.account_number})")
            
            # Check if we need to switch accounts or refresh session
            if self._needs_account_switch(account) or self._needs_session_refresh():
                if not await self._switch_to_account(account, mt5_client):
                    return False
            
            # Verify the session is still valid
            if not await self._verify_session(account):
                logger.warning(f"⚠️ SESSION: Session verification failed, attempting reconnection")
                if not await self._switch_to_account(account, mt5_client):
                    return False
            
            logger.info(f"✅ SESSION: Valid session confirmed for account {account.account_number}")
            return True
    
    def _needs_account_switch(self, account: TradingAccount) -> bool:
        """Check if we need to switch to a different account"""
        if self.current_session_account is None:
            logger.info(f"🔄 SESSION: No current session, need to login to {account.account_number}")
            return True
        
        if self.current_session_account.account_number != account.account_number:
            logger.info(f"🔄 SESSION: Account switch needed: {self.current_session_account.account_number} -> {account.account_number}")
            return True
        
        return False
    
    def _needs_session_refresh(self) -> bool:
        """Check if the current session needs to be refreshed"""
        if self.session_start_time is None:
            return True
        
        session_age = (datetime.now() - self.session_start_time).total_seconds()
        if session_age > self.max_session_duration:
            logger.info(f"🔄 SESSION: Session expired ({session_age:.0f}s > {self.max_session_duration}s)")
            return True
        
        return False
    
    async def _switch_to_account(self, account: TradingAccount, mt5_client) -> bool:
        """Switch to the specified account with retries and diagnostics"""
        # Run diagnostics on the account switch
        diagnosis = await account_switch_diagnostics.diagnose_account_switch(
            self.current_session_account, account
        )

        if diagnosis['success']:
            self.current_session_account = account
            self.session_start_time = datetime.now()
            logger.info(f"✅ SESSION: Successfully switched to account {account.account_number}")
            return True
        else:
            logger.error(f"❌ SESSION: Account switch failed after diagnostics")
            logger.error(f"❌ SESSION: Issues found: {diagnosis['issues_found']}")
            logger.info(f"💡 SESSION: Recommendations: {diagnosis['recommendations']}")

            # Log diagnostic summary for troubleshooting
            summary = account_switch_diagnostics.get_diagnostic_summary()
            logger.info(f"📊 SESSION: Switch success rate: {summary['success_rate']:.1f}% ({summary['successful_switches']}/{summary['total_attempts']})")

            return False
    
    async def _verify_session(self, account: TradingAccount) -> bool:
        """Verify the current session is valid"""
        try:
            # Check terminal connection
            terminal_info = mt5.terminal_info()
            if terminal_info is None or not terminal_info.connected:
                logger.warning("⚠️ SESSION: Terminal not connected")
                return False
            
            # Check account info
            account_info = mt5.account_info()
            if account_info is None:
                logger.warning("⚠️ SESSION: Cannot get account info")
                return False
            
            # Verify we're logged into the correct account
            if account_info.login != account.account_number:
                logger.warning(f"⚠️ SESSION: Account mismatch - expected {account.account_number}, got {account_info.login}")
                return False
            
            # Check trading permissions
            if not account_info.trade_allowed:
                logger.warning(f"⚠️ SESSION: Trading not allowed for account {account.account_number}")
                return False
            
            logger.debug(f"✅ SESSION: Session verification passed for account {account.account_number}")
            return True
            
        except Exception as e:
            logger.error(f"❌ SESSION: Exception during session verification: {e}")
            return False
    
    @asynccontextmanager
    async def account_session(self, account: TradingAccount, mt5_client):
        """Context manager for account sessions"""
        session_acquired = False
        try:
            # Acquire session
            session_acquired = await self.ensure_account_session(account, mt5_client)
            if not session_acquired:
                raise Exception(f"Failed to acquire session for account {account.account_number}")
            
            logger.info(f"🔒 SESSION: Session acquired for account {account.account_number}")
            yield mt5_client
            
        except Exception as e:
            logger.error(f"❌ SESSION: Error in account session: {e}")
            raise
        finally:
            # Session cleanup is minimal - we keep the connection open for reuse
            logger.info(f"🔓 SESSION: Session released for account {account.account_number}")
    
    async def cleanup_session(self):
        """Clean up the current session"""
        async with self.session_lock:
            logger.info("🧹 SESSION: Cleaning up session")
            self.current_session_account = None
            self.session_start_time = None
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information"""
        session_age = None
        if self.session_start_time:
            session_age = (datetime.now() - self.session_start_time).total_seconds()
        
        return {
            'current_account': self.current_session_account.account_number if self.current_session_account else None,
            'session_start_time': self.session_start_time.isoformat() if self.session_start_time else None,
            'session_age_seconds': session_age,
            'max_session_duration': self.max_session_duration,
            'needs_refresh': self._needs_session_refresh()
        }


# Global session manager instance
session_manager = MT5SessionManager()
