"""
Account Management Data Models
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from enum import Enum
from datetime import datetime

class AccountStatus(Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    ERROR = "error"

@dataclass
class TradingAccount:
    """Trading account configuration"""
    account_id: str
    account_number: int
    server: str
    username: str
    password: str
    
    # Trading configuration
    strategy_type: str
    money_management_type: str
    symbols: List[str]
    timeframes: List[str]
    
    # Money management settings
    money_management_config: Dict[str, Any] = field(default_factory=dict)
    
    # Account limits and settings
    max_daily_trades: int = 10
    max_concurrent_positions: int = 5
    trading_enabled: bool = True
    
    # Status and metadata
    status: AccountStatus = AccountStatus.ACTIVE
    last_login: Optional[datetime] = None
    last_trade: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    # Performance tracking
    total_trades: int = 0
    winning_trades: int = 0
    total_profit: float = 0.0
    max_drawdown: float = 0.0

@dataclass
class AccountGroup:
    """Group of accounts with same strategy and money management"""
    group_id: str
    strategy_type: str
    money_management_type: str
    accounts: List[str] = field(default_factory=list)
    last_ai_request: Optional[datetime] = None
    shared_signals: List[Dict[str, Any]] = field(default_factory=list)

@dataclass
class AccountBalance:
    """Account balance and margin information"""
    account_id: str
    balance: float
    equity: float
    margin: float
    free_margin: float
    margin_level: float
    currency: str
    leverage: int
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class AccountPerformance:
    """Account performance metrics"""
    account_id: str
    period_start: datetime
    period_end: datetime
    
    # Trade statistics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # Financial metrics
    total_profit: float
    gross_profit: float
    gross_loss: float
    profit_factor: float
    
    # Risk metrics
    max_drawdown: float
    max_drawdown_percent: float
    sharpe_ratio: float
    
    # Trade analysis
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    avg_trade_duration: float
