"""
Account Manager - Manages trading accounts and configurations
"""

import json
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path

from account_management.models import TradingAccount, AccountGroup, AccountBalance, AccountStatus
from logging_system.logger import get_logger
from strategies.factory import StrategyFactory
from money_management.factory import MoneyManagementFactory

logger = get_logger(__name__)

class AccountManager:
    """Manages trading accounts and their configurations"""
    
    def __init__(self, config_path: str = "config/accounts.json"):
        self.config_path = Path(config_path)
        self.accounts: Dict[str, TradingAccount] = {}
        self.account_groups: Dict[str, AccountGroup] = {}
        self.account_balances: Dict[str, AccountBalance] = {}

        # Initialize factories
        self.strategy_factory = StrategyFactory()
        self.money_management_factory = MoneyManagementFactory()

        # Ensure config directory exists
        self.config_path.parent.mkdir(parents=True, exist_ok=True)

        # Load existing accounts will be called explicitly by the main system
    
    def load_accounts(self) -> bool:
        """Load accounts from configuration file"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    data = json.load(f)

                # Load accounts
                for account_data in data.get('accounts', []):
                    # Map JSON fields to model fields
                    mapped_data = {
                        'account_id': account_data.get('account_id'),
                        'account_number': account_data.get('account_number'),
                        'server': account_data.get('server'),
                        'username': account_data.get('username', ''),
                        'password': account_data.get('password'),
                        'strategy_type': account_data.get('strategy', account_data.get('strategy_type')),
                        'money_management_type': account_data.get('money_management', account_data.get('money_management_type')),
                        'symbols': [s.get('symbol') if isinstance(s, dict) else s for s in account_data.get('symbols', [])],
                        'timeframes': [s.get('timeframe', 'H1') if isinstance(s, dict) else 'H1' for s in account_data.get('symbols', [])],
                        'money_management_config': account_data.get('money_management_config', {}),
                        'max_daily_trades': account_data.get('max_daily_trades', 10),
                        'max_concurrent_positions': account_data.get('max_concurrent_positions', 5),
                        'trading_enabled': account_data.get('trading_enabled', True)
                    }

                    account = TradingAccount(**mapped_data)
                    self.accounts[account.account_id] = account

                # Load account groups
                for group_data in data.get('groups', []):
                    group = AccountGroup(**group_data)
                    self.account_groups[group.group_id] = group

                logger.info(f"Loaded {len(self.accounts)} accounts and {len(self.account_groups)} groups")
                return True
            else:
                logger.info("No existing account configuration found, starting fresh")
                self._create_sample_config()
                return True

        except Exception as e:
            logger.error(f"Error loading accounts: {e}")
            self._create_sample_config()
            return False
    
    def save_accounts(self) -> None:
        """Save accounts to configuration file"""
        try:
            data = {
                'accounts': [account.__dict__ for account in self.accounts.values()],
                'groups': [group.__dict__ for group in self.account_groups.values()],
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.config_path, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            logger.info(f"Saved {len(self.accounts)} accounts to {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error saving accounts: {e}")
    
    def add_account(self, account: TradingAccount) -> bool:
        """Add a new trading account"""
        try:
            if account.account_id in self.accounts:
                logger.warning(f"Account {account.account_id} already exists")
                return False
            
            self.accounts[account.account_id] = account
            self._update_account_groups()
            self.save_accounts()
            
            logger.info(f"Added new account: {account.account_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding account {account.account_id}: {e}")
            return False
    
    def get_account(self, account_id: str) -> Optional[TradingAccount]:
        """Get account by ID"""
        if not account_id or not isinstance(account_id, str):
            logger.warning(f"Invalid account_id provided: {account_id}")
            return None
        return self.accounts.get(account_id)
    
    def get_active_accounts(self) -> List[TradingAccount]:
        """Get all active trading accounts"""
        return [account for account in self.accounts.values() 
                if account.status == AccountStatus.ACTIVE and account.trading_enabled]
    
    def get_accounts_by_strategy(self, strategy_type: str) -> List[TradingAccount]:
        """Get accounts using specific strategy"""
        return [account for account in self.accounts.values() 
                if account.strategy_type == strategy_type and account.status == AccountStatus.ACTIVE]
    
    def get_accounts_by_group(self, group_id: str) -> List[TradingAccount]:
        """Get accounts in specific group"""
        if not group_id or not isinstance(group_id, str):
            logger.warning(f"Invalid group_id provided: {group_id}")
            return []

        group = self.account_groups.get(group_id)
        if not group:
            logger.debug(f"Group not found: {group_id}")
            return []

        return [self.accounts[account_id] for account_id in group.accounts
                if account_id in self.accounts]
    
    def get_optimized_groups(self) -> List[Tuple[str, List[TradingAccount]]]:
        """Get account groups optimized for AI requests"""
        groups = []
        
        for group_id, group in self.account_groups.items():
            accounts = self.get_accounts_by_group(group_id)
            if accounts:
                groups.append((group_id, accounts))
        
        return groups
    
    def update_account_balance(self, account_id: str, balance: AccountBalance) -> None:
        """Update account balance information"""
        self.account_balances[account_id] = balance
        
        # Update account last activity
        if account_id in self.accounts:
            self.accounts[account_id].last_login = datetime.now()
    
    def _update_account_groups(self) -> None:
        """Update account groups based on strategy and money management"""
        # Clear existing groups
        self.account_groups.clear()
        
        # Group accounts by strategy + money management combination
        groups_map = {}
        
        for account in self.accounts.values():
            if account.status != AccountStatus.ACTIVE:
                continue
            
            group_key = f"{account.strategy_type}_{account.money_management_type}"
            
            if group_key not in groups_map:
                groups_map[group_key] = AccountGroup(
                    group_id=group_key,
                    strategy_type=account.strategy_type,
                    money_management_type=account.money_management_type
                )
            
            groups_map[group_key].accounts.append(account.account_id)
        
        self.account_groups = groups_map
        logger.info(f"Updated account groups: {len(self.account_groups)} groups created")
    
    def _create_sample_config(self) -> None:
        """Create sample configuration template"""
        logger.error(f"Configuration file not found: {self.config_path}")
        logger.error("Please create config/accounts.json with your trading accounts")
        logger.error("Example configuration:")
        logger.error("""
{
  "accounts": [
    {
      "account_id": "your_account_1",
      "account_number": ********,
      "password": "your_password",
      "server": "YourBroker-Server",
      "strategy": "trend_following",
      "money_management": "percent_risk",
      "symbols": [
        {
          "symbol": "EURUSD",
          "timeframe": "H1"
        }
      ],
      "money_management_settings": {
        "risk_percent": 2.0
      }
    }
  ]
}
        """)

    def get_account_groups(self) -> List[AccountGroup]:
        """Get all account groups"""
        return list(self.account_groups.values())

    def get_all_accounts(self) -> List[TradingAccount]:
        """Get all trading accounts"""
        return list(self.accounts.values())

    def get_account_balance(self, account_id: str) -> Optional[AccountBalance]:
        """Get account balance"""
        return self.account_balances.get(account_id)
