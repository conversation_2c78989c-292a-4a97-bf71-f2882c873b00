"""
AI-Driven Signal Generation System
"""

import asyncio
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import schedule
import time

from ai_integration.qwen_client import QwenClient
from ai_integration.prompt_builder import Prompt<PERSON>uilder
from mt5_integration.mt5_client import MT5<PERSON>lient
from account_management.account_manager import AccountManager
from strategies.base_strategy import MarketData
from money_management.base_strategy import AccountInfo
from logging_system.logger import get_logger, trading_logger
from validation.trade_validator import TradeValidator, ValidationResult
from mt5_integration.session_manager import session_manager

logger = get_logger(__name__)

class SignalGenerator:
    """Generates trading signals using AI for multiple accounts"""
    
    def __init__(self, account_manager: AccountManager):
        self.account_manager = account_manager
        self.prompt_builder = PromptBuilder()
        self.mt5_client = MT5Client()
        self.trade_validator = TradeValidator()
        self.running = False
        self.last_signal_time = {}  # Track last signal time per symbol
        self.min_signal_interval = int(os.getenv('MIN_SIGNAL_INTERVAL_MINUTES', '15'))  # Prevent overtrading

        # Load default risk management settings from environment (fallbacks)
        self.default_max_daily_trades = int(os.getenv('MAX_DAILY_TRADES_PER_ACCOUNT', '5'))
        self.default_max_open_positions = int(os.getenv('MAX_OPEN_POSITIONS', '3'))
        self.default_max_pending_orders = int(os.getenv('MAX_PENDING_ORDERS', '5'))
        self.default_max_daily_loss = float(os.getenv('MAX_DAILY_LOSS_DOLLARS', '5.0'))
        self.default_max_drawdown_percent = float(os.getenv('MAX_DRAWDOWN_PERCENT', '10.0'))

        # Track daily statistics
        self.daily_trades = {}  # account_id -> trade_count
        self.daily_pnl = {}     # account_id -> daily_pnl

        # Market hours configuration
        self.market_hours = {
            'forex': {
                'start_hour': 0,  # Sunday 22:00 GMT (Monday 00:00)
                'end_hour': 22,   # Friday 22:00 GMT
                'weekend_start': 5,  # Friday
                'weekend_end': 0     # Sunday
            }
        }
    
    async def start(self):
        """Start the signal generation system"""
        try:
            logger.info("Starting signal generation system...")
            
            # Initialize MT5
            if not self.mt5_client.initialize():
                logger.error("Failed to initialize MT5")
                return False
            
            self.running = True
            
            # Schedule signal generation
            schedule.every(self.min_signal_interval).minutes.do(self._schedule_signal_generation)
            
            # Start scheduler loop
            while self.running:
                schedule.run_pending()
                await asyncio.sleep(60)  # Check every minute
            
            logger.info("Signal generation system stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error starting signal generator: {e}")
            return False
    
    def stop(self):
        """Stop the signal generation system"""
        self.running = False
        self.mt5_client.shutdown()
        logger.info("Signal generation system stop requested")
    
    def _schedule_signal_generation(self):
        """Schedule signal generation (called by scheduler)"""
        try:
            # Run async signal generation in event loop
            asyncio.create_task(self.generate_signals())
        except Exception as e:
            logger.error(f"Error scheduling signal generation: {e}")
    
    async def generate_signals(self):
        """Generate trading signals for all account groups"""
        try:
            if not self._is_market_open():
                logger.info("Market is closed, skipping signal generation")
                return
            
            logger.info("Starting signal generation cycle...")
            
            # Get account groups for optimization
            account_groups = self.account_manager.get_account_groups()

            # If no groups exist, create virtual groups from individual accounts
            if not account_groups:
                logger.info("No account groups found, creating virtual groups from individual accounts")
                account_groups = self._create_virtual_groups()

            async with QwenClient() as qwen_client:
                for group in account_groups:
                    await self._process_account_group(group, qwen_client)
            
            logger.info("Signal generation cycle completed")
            
        except Exception as e:
            logger.error(f"Error in signal generation: {e}")

    def _create_virtual_groups(self) -> List[Dict[str, Any]]:
        """Create virtual groups from individual accounts by grouping by strategy/money management"""
        groups = {}

        for account in self.account_manager.get_all_accounts():
            key = f"{account.strategy_type}_{account.money_management_type}"

            if key not in groups:
                groups[key] = {
                    'group_id': key,
                    'strategy': account.strategy_type,
                    'money_management': account.money_management_type,
                    'accounts': []
                }

            # Convert account to dict format expected by the processor
            account_dict = {
                'account_id': account.account_id,
                'account_number': account.account_number,
                'server': account.server,
                'password': account.password,
                'symbols': [{'symbol': symbol, 'timeframe': timeframe}
                           for symbol, timeframe in zip(account.symbols, account.timeframes)],
                'money_management_config': account.money_management_config
            }
            groups[key]['accounts'].append(account_dict)

        logger.info(f"Created {len(groups)} virtual groups from individual accounts")
        return list(groups.values())

    async def _process_account_group(self, group: Dict[str, Any], qwen_client: QwenClient):
        """Process a group of accounts with same strategy/money management"""
        try:
            strategy_name = group['strategy']
            money_management_name = group['money_management']
            accounts = group['accounts']
            
            logger.info(f"Processing group: {strategy_name}/{money_management_name} with {len(accounts)} accounts")
            
            # Get strategy and money management instances
            from strategies.base_strategy import StrategyType
            from money_management.base_strategy import MoneyManagementType

            # Convert string names to enum types
            strategy_type = StrategyType(strategy_name)
            mm_type = MoneyManagementType(money_management_name)

            # Create instances with empty config (will be populated later)
            strategy = self.account_manager.strategy_factory.create_strategy(strategy_type, {})
            money_management = self.account_manager.money_management_factory.create_strategy(mm_type, {})
            
            if not strategy or not money_management:
                logger.error(f"Failed to create strategy or money management for group")
                return
            
            # Process each symbol/timeframe combination for this group
            symbols_timeframes = set()
            for account in accounts:
                for symbol_config in account['symbols']:
                    symbols_timeframes.add((symbol_config['symbol'], symbol_config['timeframe']))
            
            for symbol, timeframe in symbols_timeframes:
                await self._generate_signal_for_symbol(
                    symbol, timeframe, strategy, money_management, accounts, qwen_client
                )
            
        except Exception as e:
            logger.error(f"Error processing account group: {e}")
    
    async def _generate_signal_for_symbol(
        self,
        symbol: str,
        timeframe: str,
        strategy,
        money_management,
        accounts: List,
        qwen_client: QwenClient
    ):
        """Generate signal for specific symbol/timeframe"""
        try:
            # Check if we should generate signal (prevent overtrading)
            signal_key = f"{symbol}_{timeframe}_{strategy.strategy_type.value}_{money_management.strategy_type.value}"

            trading_logger.log_system_event(
                "SIGNAL_GENERATION_START",
                f"Starting signal generation for {signal_key} with {len(accounts)} accounts"
            )

            if not self._should_generate_signal(signal_key):
                trading_logger.log_system_event(
                    "SIGNAL_GENERATION_SKIP",
                    f"Skipping {signal_key} - signal interval not met"
                )
                return

            # Login to first account to get market data
            first_account = accounts[0]
            # Convert dict to TradingAccount object for MT5 client
            from account_management.models import TradingAccount
            account_obj = TradingAccount(
                account_id=first_account['account_id'],
                account_number=first_account['account_number'],
                server=first_account['server'],
                username=first_account.get('username', ''),
                password=first_account['password'],
                strategy_type=first_account.get('strategy_type', ''),
                money_management_type=first_account.get('money_management_type', ''),
                symbols=first_account.get('symbols', []),
                timeframes=first_account.get('timeframes', []),
                money_management_config=first_account.get('money_management_config', {})
            )
            if not self.mt5_client.login(account_obj):
                logger.error(f"Failed to login to account {first_account['account_id']}")
                return
            
            # Get market data
            start_time = time.time()
            market_data_dict = self.mt5_client.get_market_data(symbol, timeframe, 200)
            data_retrieval_time = time.time() - start_time

            if not market_data_dict:
                logger.error(f"Failed to get market data for {symbol}")
                trading_logger.log_market_data_retrieval(
                    first_account['account_id'],
                    symbol,
                    timeframe,
                    0,
                    success=False,
                    error="Failed to retrieve market data",
                    processing_time=data_retrieval_time
                )
                return

            trading_logger.log_market_data_retrieval(
                first_account['account_id'],
                symbol,
                timeframe,
                len(market_data_dict.get('candles', [])),
                success=True,
                processing_time=data_retrieval_time
            )
            
            # Convert to MarketData object
            market_data = MarketData(
                symbol=market_data_dict['symbol'],
                timeframe=market_data_dict['timeframe'],
                candles=market_data_dict['candles'],
                current_price=market_data_dict['current_price'],
                spread=market_data_dict['spread'],
                volume=market_data_dict['volume'],
                volatility=market_data_dict['volatility']
            )
            
            # Get trade history
            trade_history = self.mt5_client.get_trade_history(30)
            
            # Filter trade history by strategy magic number
            strategy_trades = [
                trade for trade in trade_history 
                if trade.get('magic_number') == strategy.magic_number
            ]
            
            # Get account info
            account_balance = self.mt5_client.get_account_info()
            if not account_balance:
                logger.error(f"Failed to get account info")
                return
            
            account_info = AccountInfo(
                balance=account_balance.balance,
                equity=account_balance.equity,
                margin=account_balance.margin,
                free_margin=account_balance.free_margin,
                margin_level=account_balance.margin_level,
                currency=account_balance.currency,
                leverage=account_balance.leverage
            )
            
            # Build AI prompt
            prompt = self.prompt_builder.build_trading_prompt(
                strategy=strategy,
                money_management=money_management,
                market_data=market_data,
                trade_history=strategy_trades,
                account_info=account_info,
                additional_context={
                    'accounts_count': len(accounts),
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'market_volatility': market_data.volatility
                }
            )
            
            # Get AI decision
            start_time = time.time()
            ai_response = await qwen_client.generate_trading_decision(prompt)
            processing_time = time.time() - start_time
            
            # Log AI decision
            trading_logger.log_ai_decision(
                first_account['account_id'],
                symbol,
                strategy.strategy_type.value,
                len(prompt),
                ai_response,
                processing_time
            )
            
            # Process signal for all accounts in group
            if ai_response.get('action') in ['BUY', 'SELL']:
                await self._distribute_signal_to_accounts(
                    ai_response, symbol, timeframe, strategy, money_management, accounts
                )
            
            # Update last signal time
            self.last_signal_time[signal_key] = datetime.now()
            
        except Exception as e:
            logger.error(f"Error generating signal for {symbol}: {e}")
            trading_logger.log_ai_error(
                accounts[0]['account_id'] if accounts else "unknown",
                symbol,
                str(e)
            )
    
    async def _distribute_signal_to_accounts(
        self,
        signal: Dict[str, Any],
        symbol: str,
        timeframe: str,
        strategy,
        money_management,
        accounts: List
    ):
        """Distribute trading signal to all accounts in group"""
        try:
            for account in accounts:
                # Check if this symbol is configured for this account
                account_symbols = [s['symbol'] for s in account['symbols']]
                if symbol not in account_symbols:
                    continue
                
                # Log signal for this account
                trading_logger.log_trade_signal(
                    account['account_id'],
                    symbol,
                    signal,
                    strategy.strategy_type.value,
                    money_management.strategy_type.value
                )
                
                # Store signal for execution (will be processed by trade manager)
                await self._store_signal_for_execution(account, symbol, signal, strategy, money_management)
            
        except Exception as e:
            logger.error(f"Error distributing signal: {e}")
    
    async def _store_signal_for_execution(
        self,
        account,
        symbol: str,
        signal: Dict[str, Any],
        strategy,
        money_management
    ):
        """Execute signal immediately"""
        try:
            # Execute signal immediately instead of storing
            await self._execute_signal(account, symbol, signal, strategy, money_management)

        except Exception as e:
            logger.error(f"Error executing signal: {e}")

    def _validate_signal(self, signal: Dict[str, Any]) -> bool:
        """Validate signal structure and content"""
        try:
            # Check required fields
            required_fields = ['action', 'confidence']
            for field in required_fields:
                if field not in signal:
                    logger.warning(f"Missing required field in signal: {field}")
                    return False

            # Validate action
            valid_actions = ['BUY', 'SELL', 'HOLD', 'CLOSE']
            if signal['action'] not in valid_actions:
                logger.warning(f"Invalid action in signal: {signal['action']}")
                return False

            # Validate confidence
            confidence = signal.get('confidence', 0)
            if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 1:
                logger.warning(f"Invalid confidence in signal: {confidence}")
                return False

            # Validate prices if present
            for price_field in ['entry_price', 'stop_loss', 'take_profit']:
                if price_field in signal:
                    price = signal[price_field]
                    if price is not None and (not isinstance(price, (int, float)) or price <= 0):
                        logger.warning(f"Invalid {price_field} in signal: {price}")
                        return False

            return True

        except Exception as e:
            logger.error(f"Error validating signal: {e}")
            return False

    async def _execute_signal(
        self,
        account,
        symbol: str,
        signal: Dict[str, Any],
        strategy,
        money_management
    ):
        """Execute trading signal immediately"""
        try:
            # Validate signal structure
            if not self._validate_signal(signal):
                logger.warning(f"Invalid signal structure for {account['account_id']}")
                trading_logger.log_signal_validation(
                    account['account_id'], symbol, signal, False, ["Invalid signal structure"]
                )
                return

            action = signal.get('action')
            trading_logger.log_system_event(
                "SIGNAL_EXECUTION_START",
                f"Starting signal execution for {account['account_id']} - {action} {symbol}"
            )

            if action not in ['BUY', 'SELL']:
                logger.debug(f"No action required for signal: {action}")
                trading_logger.log_system_event(
                    "SIGNAL_EXECUTION_SKIP",
                    f"No action required for {account['account_id']} - signal action: {action}"
                )
                return

            # Check if signal has multiple take profit levels for risk assessment
            is_multiple_tp = signal.get('take_profit_levels') and isinstance(signal.get('take_profit_levels'), list)

            # Check risk management limits before executing with multiple TP awareness
            if not self._check_risk_limits(account, is_multiple_tp):
                logger.warning(f"Risk limits exceeded for account {account['account_id']}, skipping signal")
                trading_logger.log_system_event(
                    "SIGNAL_EXECUTION_BLOCKED",
                    f"Risk limits exceeded for {account['account_id']} - {action} {symbol}",
                    "WARNING"
                )
                return

            # Convert dict to TradingAccount object for session management
            from account_management.models import TradingAccount
            account_obj = TradingAccount(
                account_id=account['account_id'],
                account_number=account['account_number'],
                server=account['server'],
                username=account.get('username', ''),
                password=account['password'],
                strategy_type=account.get('strategy_type', ''),
                money_management_type=account.get('money_management_type', ''),
                symbols=account.get('symbols', []),
                timeframes=account.get('timeframes', []),
                money_management_config=account.get('money_management_config', {})
            )

            # Use session manager for account operations
            async with session_manager.account_session(account_obj, self.mt5_client) as mt5_client:
                logger.info(f"🔒 Using managed session for account {account['account_id']}")

                # Get account balance for position sizing
                account_balance = mt5_client.get_account_info()
                if not account_balance:
                    logger.error(f"Failed to get account balance for {account['account_id']}")
                    return


                # Calculate position size using money management
                account_info = AccountInfo(
                    balance=account_balance.balance,
                    equity=account_balance.equity,
                    margin=account_balance.margin,
                    free_margin=account_balance.free_margin,
                    margin_level=account_balance.margin_level,
                    currency=account_balance.currency,
                    leverage=account_balance.leverage
                )

                # Get symbol configuration
                symbol_config = None
                for config in account['symbols']:
                    if config['symbol'] == symbol:
                        symbol_config = config
                        break

                if not symbol_config:
                    logger.error(f"Symbol {symbol} not configured for account {account['account_id']}")
                    return

                # Calculate position size
                trade_params = money_management.calculate_position_size(
                    account_info=account_info,
                    symbol=symbol,
                    entry_price=signal.get('entry_price', 0),
                    stop_loss=signal.get('stop_loss', 0),
                    trade_history=[],  # TODO: Get actual trade history
                    market_data={}  # TODO: Get actual market data
                )

                if not trade_params or trade_params.volume <= 0:
                    logger.warning(f"Invalid position size calculated for {account['account_id']}")
                    return


                # Check if signal has multiple take profit levels
                if signal.get('take_profit_levels') and isinstance(signal.get('take_profit_levels'), list):
                    tp_levels = signal['take_profit_levels']
                    logger.info(f"🎯 MULTI_TP_SIGNAL: {len(tp_levels)} TP levels detected for {symbol}")
                    for i, level in enumerate(tp_levels, 1):
                        logger.info(f"  📊 TP{i}: Price {level.get('price')}, Volume {level.get('volume_percent')}%")
                    success = await self._execute_multiple_tp_signal(
                        mt5_client, signal, symbol, action, trade_params, strategy, money_management, account
                    )
                    if success:
                        logger.info(f"✅ Multiple TP signal executed successfully for account {account['account_id']}")
                        self._update_daily_stats(account['account_id'], is_multiple_tp=True)
                    else:
                        logger.error(f"❌ Failed to execute multiple TP signal for account {account['account_id']}")
                else:
                    # Single take profit execution (existing logic)
                    success = await self._execute_single_tp_signal(
                        mt5_client, signal, symbol, action, trade_params, strategy, money_management, account
                    )
                    if success:
                        logger.info(f"✅ Single TP signal executed successfully for account {account['account_id']}")
                        self._update_daily_stats(account['account_id'], is_multiple_tp=False)
                    else:
                        logger.error(f"❌ Failed to execute single TP signal for account {account['account_id']}")

        except Exception as e:
            logger.error(f"Error executing signal for account {account['account_id']}: {e}")

    async def _execute_single_tp_signal(
        self,
        mt5_client,
        signal: Dict[str, Any],
        symbol: str,
        action: str,
        trade_params,
        strategy,
        money_management,
        account: Dict[str, Any]
    ) -> bool:
        """Execute signal with single take profit level"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            logger.info(f"🔍 SINGLE_TP: Attempt {retry_count + 1}/{max_retries} for account {account['account_id']}")

            # Validate the trade
            validation_result = self.trade_validator.validate_signal_execution(
                symbol=symbol,
                action=action,
                volume=trade_params.volume,
                price=signal.get('entry_price'),
                stop_loss=signal.get('stop_loss'),
                take_profit=signal.get('take_profit'),
                magic_number=strategy.magic_number
            )

            if validation_result.result == ValidationResult.INVALID:
                logger.error(f"❌ SINGLE_TP: Trade validation failed for account {account['account_id']}")
                for error in validation_result.errors:
                    logger.error(f"❌ SINGLE_TP: {error.error_type}: {error.message}")
                return False

            # Use corrected parameters if available
            final_params = {
                'symbol': symbol,
                'action': action,
                'volume': trade_params.volume,
                'price': signal.get('entry_price'),
                'stop_loss': signal.get('stop_loss'),
                'take_profit': signal.get('take_profit'),
                'magic_number': strategy.magic_number,
                'comment': f"AI_{strategy.strategy_type.value}_{money_management.strategy_type.value}"
            }

            if validation_result.corrected_params:
                final_params.update(validation_result.corrected_params)
                logger.info(f"🔍 SINGLE_TP: Using corrected parameters: {validation_result.corrected_params}")

            # Place order with validated/corrected parameters
            order_id = mt5_client.place_order(**final_params)

            if order_id:
                logger.info(f"✅ SINGLE_TP: Order placed successfully: {order_id}")
                return True
            else:
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"⚠️ SINGLE_TP: Order placement failed, retrying... ({retry_count}/{max_retries})")
                    await asyncio.sleep(1)
                else:
                    logger.error(f"❌ SINGLE_TP: Failed after {max_retries} attempts")

        return False

    async def _execute_multiple_tp_signal(
        self,
        mt5_client,
        signal: Dict[str, Any],
        symbol: str,
        action: str,
        trade_params,
        strategy,
        money_management,
        account: Dict[str, Any]
    ) -> bool:
        """Execute signal with multiple take profit levels"""
        tp_levels = signal.get('take_profit_levels', [])

        # Validate TP levels structure
        if not self._validate_tp_levels(tp_levels):
            logger.error(f"❌ MULTI_TP: Invalid TP levels structure for account {account['account_id']}")
            return False

        logger.info(f"🎯 MULTI_TP: Executing {len(tp_levels)} TP levels for account {account['account_id']}")

        successful_orders = []
        total_volume = trade_params.volume

        for i, tp_level in enumerate(tp_levels, 1):
            tp_price = tp_level['price']
            volume_percent = tp_level['volume_percent']
            tp_volume = round(total_volume * (volume_percent / 100), 2)

            logger.info(f"🎯 MULTI_TP: Level {i}/{len(tp_levels)} - Price: {tp_price}, Volume: {tp_volume} ({volume_percent}%)")

            # Validate this TP level
            validation_result = self.trade_validator.validate_signal_execution(
                symbol=symbol,
                action=action,
                volume=tp_volume,
                price=signal.get('entry_price'),
                stop_loss=signal.get('stop_loss'),
                take_profit=tp_price,
                magic_number=strategy.magic_number
            )

            if validation_result.result == ValidationResult.INVALID:
                logger.error(f"❌ MULTI_TP: Level {i} validation failed")
                continue

            # Prepare order parameters for this TP level
            final_params = {
                'symbol': symbol,
                'action': action,
                'volume': tp_volume,
                'price': signal.get('entry_price'),
                'stop_loss': signal.get('stop_loss'),
                'take_profit': tp_price,
                'magic_number': strategy.magic_number,
                'comment': f"AI_{strategy.strategy_type.value}_TP{i}"
            }

            if validation_result.corrected_params:
                final_params.update(validation_result.corrected_params)
                logger.info(f"🔍 MULTI_TP: Level {i} using corrected parameters")

            # Place order for this TP level
            order_id = mt5_client.place_order(**final_params)

            if order_id:
                successful_orders.append({
                    'order_id': order_id,
                    'tp_level': i,
                    'tp_price': tp_price,
                    'volume': tp_volume,
                    'volume_percent': volume_percent
                })
                logger.info(f"✅ MULTI_TP: Level {i} order placed successfully: {order_id}")
            else:
                logger.error(f"❌ MULTI_TP: Level {i} order failed")

        # Log summary
        if successful_orders:
            logger.info(f"✅ MULTI_TP: {len(successful_orders)}/{len(tp_levels)} orders placed successfully")
            for order in successful_orders:
                logger.info(f"  📊 Order {order['order_id']}: TP{order['tp_level']} @ {order['tp_price']} ({order['volume_percent']}%)")
            return True
        else:
            logger.error(f"❌ MULTI_TP: No orders placed successfully")
            return False

    def _validate_tp_levels(self, tp_levels: List[Dict[str, Any]]) -> bool:
        """Validate multiple TP levels structure"""
        if not tp_levels or not isinstance(tp_levels, list):
            logger.error("❌ TP_VALIDATION: TP levels must be a non-empty list")
            return False

        total_percent = 0
        for i, level in enumerate(tp_levels, 1):
            if not isinstance(level, dict):
                logger.error(f"❌ TP_VALIDATION: Level {i} must be a dictionary")
                return False

            if 'price' not in level or 'volume_percent' not in level:
                logger.error(f"❌ TP_VALIDATION: Level {i} missing required fields (price, volume_percent)")
                return False

            try:
                price = float(level['price'])
                volume_percent = float(level['volume_percent'])
            except (ValueError, TypeError):
                logger.error(f"❌ TP_VALIDATION: Level {i} has invalid numeric values")
                return False

            if volume_percent <= 0 or volume_percent > 100:
                logger.error(f"❌ TP_VALIDATION: Level {i} volume_percent must be between 0 and 100")
                return False

            total_percent += volume_percent

        if abs(total_percent - 100) > 0.01:  # Allow small rounding errors
            logger.error(f"❌ TP_VALIDATION: Total volume percent is {total_percent}%, must equal 100%")
            return False

        logger.info(f"✅ TP_VALIDATION: {len(tp_levels)} TP levels validated successfully")
        return True

    def _get_account_risk_settings(self, account: Dict[str, Any]) -> Dict[str, Any]:
        """Get risk management settings for specific account, with fallbacks to environment defaults"""
        money_mgmt = account.get('money_management_settings', {})

        settings = {
            'max_daily_trades': money_mgmt.get('max_daily_trades', self.default_max_daily_trades),
            'max_open_positions': money_mgmt.get('max_open_positions', self.default_max_open_positions),
            'max_pending_orders': money_mgmt.get('max_pending_orders', self.default_max_pending_orders),
            'max_daily_loss': money_mgmt.get('max_daily_loss', self.default_max_daily_loss),
            'max_drawdown_percent': money_mgmt.get('max_drawdown_percent', self.default_max_drawdown_percent)
        }

        logger.debug(f"🔧 Risk settings for account {account['account_id']}: {settings}")
        return settings

    def _check_risk_limits(self, account: Dict[str, Any], is_multiple_tp: bool = False) -> bool:
        """Check if account is within risk management limits using account-specific settings"""
        try:
            # Get account-specific risk settings
            risk_settings = self._get_account_risk_settings(account)
            account_id = account['account_id']

            # Check daily trade limit
            today = datetime.now().date()
            daily_key = f"{account_id}_{today}"

            if daily_key not in self.daily_trades:
                self.daily_trades[daily_key] = 0

            if self.daily_trades[daily_key] >= risk_settings['max_daily_trades']:
                logger.warning(f"Daily trade limit ({risk_settings['max_daily_trades']}) reached for account {account_id}")
                return False

            # Enhanced position and pending order checking
            if not self.mt5_client.current_account:
                return True  # Can't check without login, allow trade

            position_check = self._check_position_limits(account, risk_settings, is_multiple_tp)
            if not position_check:
                return False

            # Check daily loss limit
            if daily_key not in self.daily_pnl:
                self.daily_pnl[daily_key] = 0.0

            if self.daily_pnl[daily_key] <= -risk_settings['max_daily_loss']:
                logger.warning(f"Daily loss limit (${risk_settings['max_daily_loss']}) reached for account {account_id}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return True  # Allow trade if check fails

    def _check_position_limits(self, account: Dict[str, Any], risk_settings: Dict[str, Any], is_multiple_tp: bool = False) -> bool:
        """Check position and pending order limits with smart counting using account-specific settings"""
        try:
            account_id = account['account_id']
            max_open_positions = risk_settings['max_open_positions']
            max_pending_orders = risk_settings['max_pending_orders']
            # Get current positions and pending orders
            positions = self.mt5_client.get_positions() or []
            pending_orders = self.mt5_client.get_pending_orders() or []

            # Group positions and orders by magic number (strategy groups)
            position_groups = self._group_by_strategy(positions)
            pending_groups = self._group_by_strategy(pending_orders)

            # Count unique strategy groups (not individual orders)
            unique_position_groups = len(position_groups)
            unique_pending_groups = len(pending_groups)

            logger.info(f"🔍 POSITION_CHECK: Account {account_id}")
            logger.info(f"  📊 Individual positions: {len(positions)}")
            logger.info(f"  📊 Position groups (strategies): {unique_position_groups}")
            logger.info(f"  📊 Individual pending orders: {len(pending_orders)}")
            logger.info(f"  📊 Pending groups (strategies): {unique_pending_groups}")
            logger.info(f"  ⚙️ Account limits: {max_open_positions} positions, {max_pending_orders} pending orders")

            # Log detailed breakdown
            if position_groups:
                logger.info(f"  📋 Position groups by magic: {list(position_groups.keys())}")
            if pending_groups:
                logger.info(f"  📋 Pending groups by magic: {list(pending_groups.keys())}")

            # Check open position limit (count by strategy groups, not individual orders)
            total_strategy_groups = unique_position_groups
            if is_multiple_tp:
                # Multiple TP signal counts as 1 strategy group
                total_strategy_groups += 1
            else:
                # Single TP signal counts as 1 strategy group
                total_strategy_groups += 1

            if total_strategy_groups > max_open_positions:
                logger.warning(f"⚠️ Position limit would be exceeded: {total_strategy_groups}/{max_open_positions} strategy groups")
                return False

            # Check pending order limit (individual orders)
            if is_multiple_tp:
                # Multiple TP creates multiple pending orders
                estimated_new_pendings = 3  # Estimate for multiple TP
            else:
                # Single TP creates 1 pending order (if price difference requires it)
                estimated_new_pendings = 1

            total_pending_orders = len(pending_orders) + estimated_new_pendings
            if total_pending_orders > max_pending_orders:
                logger.warning(f"⚠️ Pending order limit would be exceeded: {total_pending_orders}/{max_pending_orders}")
                return False

            # Check potential activation risk
            # If all pending orders activate, would we exceed position limits?
            potential_total_groups = unique_position_groups + unique_pending_groups
            if is_multiple_tp:
                potential_total_groups += 1

            if potential_total_groups > max_open_positions:
                logger.warning(f"⚠️ Risk: If all pending orders activate, position limit would be exceeded: {potential_total_groups}/{max_open_positions}")
                # Allow but log warning - trader should manage this
                logger.warning("⚠️ Consider reducing pending orders or increasing position limits")

            logger.info(f"✅ Position limits check passed for account {account_id}")
            return True

        except Exception as e:
            logger.error(f"❌ Error checking position limits for account {account_id}: {e}")
            return False

            # Check potential activation risk
            # If all pending orders activate, would we exceed position limits?
            potential_total_groups = unique_position_groups + unique_pending_groups
            if is_multiple_tp:
                potential_total_groups += 1

            if potential_total_groups > max_open_positions:
                logger.warning(f"⚠️ Risk: If all pending orders activate, position limit would be exceeded: {potential_total_groups}/{self.max_open_positions}")
                # Allow but log warning - trader should manage this
                logger.warning("⚠️ Consider reducing pending orders or increasing position limits")

            logger.info(f"✅ Position limits check passed for account {account_id}")
            return True

        except Exception as e:
            logger.error(f"❌ Error checking position limits for account {account_id}: {e}")
            return False

    def _group_by_strategy(self, orders_or_positions: List[Dict]) -> Dict[int, List[Dict]]:
        """Group orders/positions by magic number (strategy identifier)"""
        groups = {}
        for item in orders_or_positions:
            magic = item.get('magic', 0)
            if magic not in groups:
                groups[magic] = []
            groups[magic].append(item)
        return groups

    def _update_daily_stats(self, account_id: str, pnl: float = 0.0, is_multiple_tp: bool = False):
        """Update daily trading statistics - multiple TP counts as 1 trade"""
        try:
            today = datetime.now().date()
            daily_key = f"{account_id}_{today}"

            # Update trade count
            if daily_key not in self.daily_trades:
                self.daily_trades[daily_key] = 0
            self.daily_trades[daily_key] += 1

            # Update P&L
            if daily_key not in self.daily_pnl:
                self.daily_pnl[daily_key] = 0.0
            self.daily_pnl[daily_key] += pnl

            trade_type = "Multi-TP" if is_multiple_tp else "Single-TP"
            logger.info(f"📊 Daily stats for {account_id} ({trade_type}): {self.daily_trades[daily_key]} trades, ${self.daily_pnl[daily_key]:.2f} P&L")

        except Exception as e:
            logger.error(f"Error updating daily stats: {e}")

    def _should_generate_signal(self, signal_key: str) -> bool:
        """Check if we should generate a new signal (prevent overtrading)"""
        try:
            last_time = self.last_signal_time.get(signal_key)
            if not last_time:
                return True
            
            time_diff = datetime.now() - last_time
            return time_diff.total_seconds() >= (self.min_signal_interval * 60)
            
        except Exception as e:
            logger.error(f"Error checking signal timing: {e}")
            return False
    
    def _is_market_open(self) -> bool:
        """Check if forex market is open"""
        try:
            now = datetime.now()
            weekday = now.weekday()  # 0=Monday, 6=Sunday
            hour = now.hour
            
            # Forex market is closed from Friday 22:00 to Sunday 22:00 GMT
            if weekday == 5 and hour >= 22:  # Friday after 22:00
                return False
            elif weekday == 6:  # Saturday (all day)
                return False
            elif weekday == 0 and hour < 22:  # Sunday before 22:00
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking market hours: {e}")
            return False  # Conservative approach
