"""
Percent Risk Money Management Strategy
"""

from typing import Dict, Any, Optional
from money_management.base_strategy import BaseMoneyManagement, MoneyManagementType, TradeParameters, AccountInfo

class PercentRiskStrategy(BaseMoneyManagement):
    """
    Percent Risk Money Management Strategy
    Risks a fixed percentage of account balance per trade
    """
    
    def get_strategy_type(self) -> MoneyManagementType:
        return MoneyManagementType.PERCENT_RISK
    
    def calculate_position_size(
        self, 
        account_info: AccountInfo,
        symbol: str,
        entry_price: float,
        stop_loss: Optional[float],
        trade_history: list,
        market_data: Dict[str, Any]
    ) -> TradeParameters:
        """Calculate position size based on percent risk"""
        
        risk_percent = self.config.get('risk_percent', 2.0) / 100  # Default 2%
        risk_amount = account_info.balance * risk_percent
        
        if not stop_loss:
            # If no stop loss, use default volume with calculated risk
            default_volume = self.config.get('default_volume', 0.01)
            return TradeParameters(
                volume=default_volume,
                stop_loss=None,
                take_profit=None,
                risk_amount=risk_amount,
                max_loss=risk_amount,
                confidence_level=0.3  # Lower confidence without stop loss
            )
        
        # Calculate position size based on stop loss distance
        pip_value = market_data.get('pip_value', 1.0)
        pip_size = market_data.get('pip_size', 0.0001)
        pip_difference = abs(entry_price - stop_loss) / pip_size
        
        # Calculate volume to risk the desired percentage
        if pip_difference > 0 and pip_value > 0:
            volume = risk_amount / (pip_difference * pip_value)
            # Round to appropriate lot size
            min_volume = market_data.get('min_volume', 0.01)
            max_volume = market_data.get('max_volume', 100.0)
            volume = max(min_volume, min(volume, max_volume))
            volume = round(volume / min_volume) * min_volume
        else:
            volume = self.config.get('default_volume', 0.01)
        
        return TradeParameters(
            volume=volume,
            stop_loss=stop_loss,
            take_profit=None,
            risk_amount=risk_amount,
            max_loss=risk_amount,
            confidence_level=0.8  # High confidence with proper risk management
        )
    
    def get_ai_prompt(self, account_info: AccountInfo, trade_history: list) -> str:
        """Get AI prompt for percent risk strategy"""
        
        recent_trades = trade_history[-20:] if len(trade_history) > 20 else trade_history
        win_rate = self._calculate_win_rate(recent_trades)
        avg_profit = self._calculate_average_profit(recent_trades)
        risk_reward_ratio = self._calculate_risk_reward_ratio(recent_trades)
        
        risk_percent = self.config.get('risk_percent', 2.0)
        risk_amount = account_info.balance * (risk_percent / 100)
        
        prompt = f"""
You are managing a trading account using a PERCENT RISK money management strategy.

ACCOUNT INFORMATION:
- Balance: ${account_info.balance:,.2f}
- Equity: ${account_info.equity:,.2f}
- Free Margin: ${account_info.free_margin:,.2f}
- Currency: {account_info.currency}
- Leverage: 1:{account_info.leverage}

MONEY MANAGEMENT STRATEGY: Percent Risk
- Risk per trade: {risk_percent}% of account balance
- Current risk amount: ${risk_amount:.2f} per trade
- Position size automatically calculated based on stop loss distance
- Consistent risk exposure across all trades

RECENT PERFORMANCE:
- Win Rate: {win_rate:.1f}%
- Average Profit per Trade: ${avg_profit:.2f}
- Risk/Reward Ratio: {risk_reward_ratio:.2f}
- Total Recent Trades: {len(recent_trades)}

STRATEGY GUIDELINES:
1. Always set appropriate stop losses (required for position sizing)
2. Position size will be calculated to risk exactly {risk_percent}% per trade
3. Focus on trades with minimum 1:2 risk-reward ratio
4. Tighter stops allow larger position sizes, wider stops require smaller sizes
5. Maintain discipline - never override the calculated position size

RISK MANAGEMENT RULES:
- Maximum daily risk: {self.config.get('max_daily_risk_percent', 10)}% of account
- Stop loss is MANDATORY for this strategy
- Consider market volatility when setting stop distances
- Monitor correlation between positions to avoid overexposure

POSITION SIZING LOGIC:
- Risk Amount = Account Balance × {risk_percent}%
- Position Size = Risk Amount ÷ (Stop Loss Distance in Pips × Pip Value)
- This ensures consistent risk regardless of stop loss distance

Based on the market data and performance metrics, provide your trading decisions with:
1. Entry signals with specific entry prices
2. MANDATORY stop loss levels (critical for position sizing)
3. Take profit targets (aim for minimum 1:2 risk-reward)
4. Trade management and scaling strategies
5. Market analysis and setup quality assessment

Remember: Your edge comes from consistent risk management and high-probability setups.
The system will automatically calculate position size based on your stop loss placement.
"""
        return prompt
    
    def _calculate_win_rate(self, trades: list) -> float:
        """Calculate win rate from trade history"""
        if not trades:
            return 0.0
        
        winning_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
        return (winning_trades / len(trades)) * 100
    
    def _calculate_average_profit(self, trades: list) -> float:
        """Calculate average profit from trade history"""
        if not trades:
            return 0.0
        
        total_profit = sum(trade.get('profit', 0) for trade in trades)
        return total_profit / len(trades)
    
    def _calculate_risk_reward_ratio(self, trades: list) -> float:
        """Calculate average risk-reward ratio"""
        if not trades:
            return 0.0
        
        total_ratio = 0
        valid_trades = 0
        
        for trade in trades:
            profit = trade.get('profit', 0)
            risk = trade.get('risk_amount', 0)
            if risk > 0:
                ratio = abs(profit) / risk
                total_ratio += ratio
                valid_trades += 1
        
        return total_ratio / valid_trades if valid_trades > 0 else 0.0
